using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using CoreAnnotationFormat = MedicalImageAnalysis.Core.Interfaces.AnnotationFormat;

namespace MedicalImageAnalysis.Api.Models;

/// <summary>
/// 验证标注请求
/// </summary>
public class ValidateAnnotationsRequest
{
    /// <summary>
    /// 要验证的标注列表
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 验证规则
    /// </summary>
    public AnnotationValidationRules? ValidationRules { get; set; }
}

/// <summary>
/// 转换标注格式请求
/// </summary>
public class ConvertAnnotationFormatRequest
{
    /// <summary>
    /// 要转换的标注列表
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 目标格式
    /// </summary>
    public CoreAnnotationFormat TargetFormat { get; set; } = CoreAnnotationFormat.YOLO;

    /// <summary>
    /// 图像宽度
    /// </summary>
    public int ImageWidth { get; set; }

    /// <summary>
    /// 图像高度
    /// </summary>
    public int ImageHeight { get; set; }

    /// <summary>
    /// 类别映射
    /// </summary>
    public Dictionary<string, int>? ClassMapping { get; set; }
}

/// <summary>
/// 检测异常请求
/// </summary>
public class DetectAnomaliesRequest
{
    /// <summary>
    /// 要检测的标注列表
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 检测配置
    /// </summary>
    public AnomalyDetectionConfig? DetectionConfig { get; set; }
}

/// <summary>
/// 合并重叠标注请求
/// </summary>
public class MergeOverlappingAnnotationsRequest
{
    /// <summary>
    /// 要合并的标注列表
    /// </summary>
    public List<Annotation> Annotations { get; set; } = new();

    /// <summary>
    /// 合并配置
    /// </summary>
    public AnnotationMergeConfig? MergeConfig { get; set; }
}


